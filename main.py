import shutil

import pandas as pd
import os
import matplotlib.pyplot as plt
import time
from docx import Document
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT, WD_TABLE_ALIGNMENT
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Inches, Pt
import subprocess
import numpy as np
from tqdm import tqdm



def get_excel_sheet_names(file_path):
    """
    读取 Excel 文件并返回所有工作表的名称。

    :param file_path: Excel 文件的路径
    :return: 包含所有工作表名称的列表
    """
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
    except Exception as e:
        print(f"读取 Excel 文件时出错: {e}")
    return []


def filter_sheet_names(sheet_names, keyword):
    """
    过滤出包含指定关键字的工作表名称。

    :param sheet_names: 所有工作表名称的列表
    :param keyword: 用于过滤的关键字
    :return: 包含指定关键字的工作表名称列表
    """
    return [name for name in sheet_names if keyword in name]


def read_and_combine_sheets(file_path, sheet_names):
    """
    读取指定工作表的数据并纵向合并。

    :param file_path: Excel 文件的路径
    :param sheet_names: 要读取的工作表名称列表
    :return: 合并后的 DataFrame
    """
    df_list = []
    try:
        excel_file = pd.ExcelFile(file_path)
        print("正在读取和合并工作表...")
        for sheet_name in tqdm(sheet_names, desc="读取工作表", unit="sheet"):
            df = excel_file.parse(sheet_name)
            df_list.append(df)
        return pd.concat(df_list, axis=0, ignore_index=True)
    except Exception as e:
        print(f"读取工作表数据时出错: {e}")
        return pd.DataFrame()


def print_unique_values(df, column_name):
    """
    打印指定列的不重复数据及其数量。

    :param df: 数据 DataFrame
    :param column_name: 指定列名
    """
    unique_values = []
    if column_name in df.columns:
        unique_values = df[column_name].unique()
        # print(f"{column_name} 列中不重复的数据: {unique_values}")
        # print(f"{column_name} 列中不重复数据的数量: {len(unique_values)}")
    else:
        print(f"合并后的 DataFrame 中不存在 {column_name} 列。")
    return unique_values


def calculate_and_add_columns(df):
    """
    计算并添加偏差相关列到 DataFrame。

    :param df: 数据 DataFrame
    :return: 添加列后的 DataFrame
    """
    required_columns = ['Quality Gate X', 'Nominal X', 'Quality Gate Y', 'Nominal Y',
                        'Quality Gate Z', 'Nominal Z', 'Reference X', 'Reference Y', 'Reference Z']
    if all(col in df.columns for col in required_columns):
        # 创建mapvision dev
        df['map_dev_X'] = df['Quality Gate X'] - df['Nominal X']
        df['map_dev_Y'] = df['Quality Gate Y'] - df['Nominal Y']
        df['map_dev_Z'] = df['Quality Gate Z'] - df['Nominal Z']
        # 创建cmm dev
        df['cmm_dev_X'] = df['Reference X'] - df['Nominal X']
        df['cmm_dev_Y'] = df['Reference Y'] - df['Nominal Y']
        df['cmm_dev_Z'] = df['Reference Z'] - df['Nominal Z']
        # 创建mapvision - cmm dev
        df['mapvision_cmm_dev_X'] = df['map_dev_X'] - df['cmm_dev_X']
        df['mapvision_cmm_dev_Y'] = df['map_dev_Y'] - df['cmm_dev_Y']
        df['mapvision_cmm_dev_Z'] = df['map_dev_Z'] - df['cmm_dev_Z']
        # print("计算并添加新列后的 DataFrame 前 5 行:")
        # print(df.head(5))
    else:
        missing_columns = [col for col in required_columns if col not in df.columns]
        print(f"合并后的 DataFrame 中缺少以下列: {missing_columns}，无法计算偏差列。")
    return df


# 图形输出
def fig_output(df, points):
    coordinate_list = ['X', 'Y', 'Z']
    print("正在生成图形...")
    total_combinations = len(points) * len(coordinate_list)
    with tqdm(total=total_combinations, desc="生成图形", unit="图") as pbar:
        for point in points:
            for measure_coordinate in coordinate_list:
                #  按点过滤dataframe行
                df_filter = df[df['Feature Name'] == point]
                # 强制按照sn列升序排列
                df_filter = df_filter.sort_values(by=['snplus'])
                sn_quantity = len(df_filter['Serial'].drop_duplicates().values.tolist())
                # 创建包含两个垂直子图的图形和坐标轴
                fig, (ax1, ax2) = plt.subplots(nrows=2, ncols=1, figsize=(5.6, 3.4), sharex=True)
                # 添加标题，可根据实际需求修改标题内容
                fig.suptitle(f'{point} - {measure_coordinate}', fontsize=10)
                # ax1
                # 设置 x 轴和 y 轴的标签
                # ax1.set_xlabel('snplus')
                # ax1.set_ylabel('deviation')
                # 隐藏 ax1 的 x ，y轴
                ax1.get_xaxis().set_visible(False)
                # 设置坐标轴字体大小
                ax1.tick_params(axis='both', labelsize=6)  # 将坐标轴字体大小设置为 10
                # # 设置 x 轴间距
                ax1.set_xticks(np.arange(0, (sn_quantity + 1), 1))  # x 轴间距为 1
                # 绘制四条折线
                ax1.plot(df_filter['snplus'], df_filter[f'Tolerance Upper {measure_coordinate}'], color='red', linewidth=1.5, linestyle='--')
                ax1.plot(df_filter['snplus'], df_filter[f'Tolerance Lower {measure_coordinate}'], color='red', linewidth=1.5, linestyle='--')
                ax1.plot(df_filter['snplus'], (df_filter[f'Tolerance Upper {measure_coordinate}'] + df_filter[f'Tolerance Lower {measure_coordinate}'])/2, color='green', linewidth=1.5) #根据需要调整
                ax1.plot(df_filter['snplus'], df_filter[f'map_dev_{measure_coordinate}'], color=(0/255, 248/255, 201/255), linewidth=1.5, marker='o', markersize=3) #根据需要调整
                ax1.plot(df_filter['snplus'], df_filter[f'cmm_dev_{measure_coordinate}'], color='purple', linewidth=1.5, marker='o', markersize=3) #根据需要调整

                # ax2
                # 设置 x 轴和 y 轴的标签
                ax2.set_xlabel('snplus')
                ax2.set_ylabel('deviation')
                # 设置坐标轴字体大小
                ax2.tick_params(axis='both', labelsize=6)  # 将坐标轴字体大小设置为 10
                # # 设置 x 轴间距
                ax2.set_xticks(np.arange(0, (sn_quantity + 1), 1))  # x 轴间距为 1
                # 绘制四条折线
                ax2.plot(df_filter['snplus'], df_filter[f'Tolerance Upper {measure_coordinate}'], color='red', linewidth=1.5, linestyle='--')
                ax2.plot(df_filter['snplus'], df_filter[f'Tolerance Lower {measure_coordinate}'], color='red', linewidth=1.5, linestyle='--')
                ax2.plot(df_filter['snplus'], (df_filter[f'Tolerance Upper {measure_coordinate}'] + df_filter[f'Tolerance Lower {measure_coordinate}'])/2, color='green', linewidth=1.5) #根据需要调整
                ax2.plot(df_filter['snplus'], df_filter[f'mapvision_cmm_dev_{measure_coordinate}'], color='blue', linewidth=1.5, marker='o',markersize=3)  # 根据需要调整
                for x, y in zip(df_filter['snplus'], df_filter[f'mapvision_cmm_dev_{measure_coordinate}']):
                    ax2.annotate(f'{y:.3f}',  # 显示3位小数
                                xy=(x, y),
                                xytext=(0, 5),  # 标签相对于数据点的偏移量
                                textcoords='offset points',
                                fontsize=7,  # 标签字体大小
                                rotation=90,
                                color='blue')


                # # 保存图形
                fig.savefig(cache_dir + "\\" + point + "_" + measure_coordinate + ".png")
                # 关闭图形
                plt.close()
                # 更新进度条
                pbar.update(1)


# 生成报告
def report(df, points):
    coordinate_list = ['X', 'Y', 'Z']
    # 创建 docx文档报告
    document = Document()
    print("正在生成报告...")
    total_combinations = len(points) * len(coordinate_list)
    with tqdm(total=total_combinations, desc="生成报告", unit="页") as pbar:
        for point in points:
            for measure_coordinate in coordinate_list:
                # 向docx中添加内容

                paragraph = document.add_paragraph()
                paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                run = paragraph.add_run("")
                run.add_picture(cache_dir + "\\" + point + "_" + measure_coordinate + ".png")

                # 这里列数需要根据实际数据长度调整，假设以 map_dev 数据长度为准
                data_dict = {}
                filtered_df = df[df['Feature Name'] == point]
                # 使用字典存储数据
                data_dict[f'map_dev_{measure_coordinate}'] = filtered_df[f'map_dev_{measure_coordinate}'].tolist()
                data_dict[f'cmm_dev_{measure_coordinate}'] = filtered_df[f'cmm_dev_{measure_coordinate}'].tolist()
                data_dict[f'mapvision_cmm_dev_{measure_coordinate}'] = filtered_df[f'mapvision_cmm_dev_{measure_coordinate}'].tolist()
                # 计算标准差
                std_dev = np.std(data_dict[f'mapvision_cmm_dev_{measure_coordinate}'])
                # 计算平均值
                mean_value = np.mean(data_dict[f'mapvision_cmm_dev_{measure_coordinate}'])
                paragraph = document.add_paragraph(f'{point} - {measure_coordinate} - (Stdev: {std_dev:.3f}, Bias: {mean_value:.3f})')
                paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                # 调整表格列数为数据长度
                table = document.add_table(rows=3, cols=len(sn_values) + 1,
                                           style="Table Grid")
                # 设置表头

                table.cell(0, 0).text = 'Map dev'
                table.cell(1, 0).text = 'CMM dev'
                table.cell(2, 0).text = 'MAP-CMM'
                # 填充数据
                for col_num, value in enumerate(data_dict[f'map_dev_{measure_coordinate}'], start=1):
                    table.cell(0, col_num).text = f"{value:.3f}"
                for col_num, value in enumerate(data_dict[f'cmm_dev_{measure_coordinate}'], start=1):
                    table.cell(1, col_num).text = f"{value:.3f}"
                for col_num, value in enumerate(data_dict[f'mapvision_cmm_dev_{measure_coordinate}'], start=1):
                    table.cell(2, col_num).text = f"{value:.3f}"

                # document.add_paragraph()
                # 更新进度条
                pbar.update(1)
    # 修改文件所有表格为居中，单元格居中，字体10
    for cpk_table in document.tables:
        cpk_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        for cpk_row in cpk_table.rows:
            for cpk_cell in cpk_row.cells:
                cpk_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
                cpk_cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                paragraphs = cpk_cell.paragraphs
                for paragraph in paragraphs:
                    for run in paragraph.runs:
                        font = run.font
                        font.size = Pt(10)
    save_path = 'report' + "\\" + product + '-' + time_now + '.docx'
    document.save(save_path)
    subprocess.Popen(['start', save_path], shell=True)



if __name__ == "__main__":
    print("=" * 60)
    print("数据分析和报告生成程序启动")
    print("=" * 60)

    file_path = r"D:\na6-correlation\Correlation-P04714.xlsx"
    keyword = "P0"
    product = "P04714"
    # 获取系统时间
    time_now = time.strftime('%Y%m%d%H%M%S')
    # 创建报告文件夹report_dir(产品+日期+时间),子文件夹cache_dir
    report_dir = "report\\" + product + "-" + time_now
    cache_dir = report_dir + '\\' + 'cache'

    print("正在创建输出目录...")
    os.mkdir(report_dir)
    os.mkdir(cache_dir)
    print(f"输出目录已创建: {report_dir}")

    # 获取工作表名称
    print("正在读取Excel文件...")
    sheet_names = get_excel_sheet_names(file_path)
    print(f"发现 {len(sheet_names)} 个工作表")

    # 过滤工作表名称
    filtered_sheet_names = filter_sheet_names(sheet_names, keyword)
    print(f"过滤后有 {len(filtered_sheet_names)} 个工作表包含关键字 '{keyword}'")

    # 读取并合并数据
    combined_df = read_and_combine_sheets(file_path, filtered_sheet_names)

    # 设置 Pandas 选项以显示全量数据
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', None)

    # 计算并添加偏差相关列
    print("正在计算偏差相关列...")
    combined_df = calculate_and_add_columns(combined_df)
    print("偏差计算完成")

    # 创建snplus
    print("正在处理序列号...")
    sn_values = print_unique_values(combined_df, 'Serial')
    print(f"发现 {len(sn_values)} 个唯一序列号")

    for i, sn in enumerate(tqdm(sn_values, desc="处理序列号", unit="个"), 1):
        combined_df.loc[combined_df['Serial'] == sn, 'snplus'] = i

    # 原始数据Point点
    points_0 = print_unique_values(combined_df, 'Feature Name')
    print(f"原始数据中有 {len(points_0)} 个测量点")

    # 过滤掉 Reference X, Reference Y, Reference Z 列为空的数据
    print("正在过滤缺失CMM数据的点...")
    combined_df = combined_df.dropna(subset=['Reference X', 'Reference Y', 'Reference Z'])

    # 打印 Feature Name 列不重复数据
    points = print_unique_values(combined_df, 'Feature Name')
    print(f"过滤后有 {len(points)} 个有效测量点")

    # 计算两个列表的差值
    points_0_set = set(points_0)
    points_set = set(points)
    diff_points = list(points_0_set - points_set)

    # 将 diff_points 输出到 txt 文件
    print("正在保存缺失CMM数据的点列表...")
    diff_file_path = 'report' + "\\" + 'miss_cmm_value-' + product + '-' + time_now + '.txt'
    with open(diff_file_path, 'w', encoding='utf-8') as f:
        for diff_point in diff_points:
            f.write(f"{diff_point}\n")
    print(f"缺失CMM数据的点: {len(diff_points)} 个")

    print("正在保存处理后的数据...")
    combined_df.to_excel('report\\' + product + '-' + time_now + '.xlsx')
    print("数据保存完成")



    # 生成图形和报告
    fig_output(combined_df, points)
    report(combined_df, points)

    print("\n" + "=" * 60)
    print("处理完成！")
    print(f"总共处理了 {len(points)} 个测量点")
    print(f"生成了 {len(points) * 3} 个图形")
    print(f"报告已保存")
    print("=" * 60)

    # 删除 report_dir 及其内部所有内容
    print("正在清理临时文件...")
    if os.path.exists(report_dir):
        shutil.rmtree(report_dir)
        print(f"临时目录 {report_dir} 已清理")

    # 恢复默认设置
    pd.reset_option('all')
    print("程序执行完成！")
